# Update these with your Supabase details from your project settings > API
# https://app.supabase.com/project/_/settings/api
NEXT_PUBLIC_SUPABASE_URL=https://qyumotgljdxepjyrxceh.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF5dW1vdGdsamR4ZXBqeXJ4Y2VoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwNTU0MjQsImV4cCI6MjA2MDYzMTQyNH0.g_vMGR4hBvJlcHhWKd1fwjFeKg5KhQTKYocBLFp8yF4
NEXT_PUBLIC_FORCE_LOCAL_STORAGE=false

# If using a fixed socket server location, uncomment and set this
# NEXT_PUBLIC_SOCKET_URL=http://localhost:3002

# Control the socket connection behavior
NEXT_PUBLIC_FORCE_ONLINE_MODE=false

# Supabase SMTP Configuration using Resend
SUPABASE_AUTH_SMTP_ADMIN_EMAIL=<EMAIL>
SUPABASE_AUTH_SMTP_HOST=smtp.resend.com
SUPABASE_AUTH_SMTP_PORT=465
SUPABASE_AUTH_SMTP_USER=resend
SUPABASE_AUTH_SMTP_PASS=re_9biPA7UJ_GK7zqSJbGLWcJm7NnaNB94VK
SUPABASE_AUTH_SMTP_SENDER_NAME=Snake.io