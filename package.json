{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "server": "node server.js", "game-server": "node game-server.js", "dev:server": "nodemon server.js", "dev:game-server": "nodemon game-server.js", "dev:all": "concurrently \"npm run dev\" \"npm run dev:server\""}, "dependencies": {"@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.8", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "latest", "autoprefixer": "10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "express": "^4.18.2", "lucide-react": "^0.468.0", "next": "latest", "next-themes": "^0.4.3", "prettier": "^3.3.3", "react": "19.0.0", "react-dom": "19.0.0", "react-toastify": "^11.0.5", "resend": "^4.3.0", "socket.io": "^4.7.4", "sonner": "^2.0.3", "uuid": "^9.0.1"}, "devDependencies": {"@netlify/plugin-nextjs": "^5.10.7", "@supabase/ssr": "^0.6.1", "@types/node": "22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "19.0.2", "@types/uuid": "^9.0.8", "concurrently": "^8.2.2", "nodemon": "^3.1.9", "postcss": "8.4.49", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.5.2", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "5.7.2"}}