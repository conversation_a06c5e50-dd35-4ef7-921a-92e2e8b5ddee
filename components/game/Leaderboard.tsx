"use client";

import { useState, useEffect } from 'react';
import { gameSocketClient } from '@/lib/game/socket-client';
import { Trophy, ChevronUp, ChevronDown, Crown } from 'lucide-react';

interface LeaderboardPlayer {
  id: string;
  name: string;
  score: number;
}

interface LeaderboardProps {
  playerCount?: number;
}

export default function Leaderboard({ playerCount }: LeaderboardProps) {
  const [players, setPlayers] = useState<LeaderboardPlayer[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentPlayerId, setCurrentPlayerId] = useState<string | null>(null);
  
  // Listen for leaderboard updates
  useEffect(() => {
    const handleLeaderboardUpdate = (leaderboard: LeaderboardPlayer[]) => {
      setPlayers(leaderboard);
    };
    
    // Get current player ID
    setCurrentPlayerId(gameSocketClient.getPlayerId());
    
    // Add leaderboard listener
    gameSocketClient.addLeaderboardListener(handleLeaderboardUpdate);
    
    return () => {
      gameSocketClient.removeLeaderboardListener(handleLeaderboardUpdate);
    };
  }, []);
  
  // Display top 3 players if collapsed, or all players if expanded
  const displayedPlayers = isExpanded ? players : players.slice(0, 3);
  
  // Find current player's position
  const currentPlayerPosition = players.findIndex(p => p.id === currentPlayerId);
  const showCurrentPlayerSeparately = 
    currentPlayerPosition >= 0 && 
    currentPlayerPosition >= 3 && 
    !isExpanded;
    
  return (
    <div className="w-64 md:w-72 bg-black/80 backdrop-blur-md rounded-lg shadow-xl overflow-hidden border border-indigo-500/30">
      {/* Header */}
      <div className="flex items-center justify-between bg-indigo-600/90 px-3 py-2 md:px-4 md:py-3">
        <div className="flex items-center gap-2">
          <Trophy size={14} className="text-white md:w-4 md:h-4" />
          <h3 className="text-xs md:text-sm font-bold text-white">Leaderboard</h3>
          {playerCount !== undefined && (
            <span className="ml-1 md:ml-2 bg-indigo-700/80 text-white text-xs px-2 py-1 rounded-full font-medium">
              {playerCount}
            </span>
          )}
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-white hover:text-indigo-200 transition-colors p-1 rounded hover:bg-indigo-700/50"
        >
          {isExpanded ? <ChevronUp size={14} className="md:w-4 md:h-4" /> : <ChevronDown size={14} className="md:w-4 md:h-4" />}
        </button>
      </div>

      {/* Player list */}
      <div className="p-3 space-y-2">
        {players.length === 0 ? (
          <div className="text-center text-sm text-gray-400 py-4">
            No players yet
          </div>
        ) : (
          <>
            {displayedPlayers.map((player, index) => (
              <div
                key={player.id}
                className={`flex items-center px-3 py-2 rounded-lg transition-all duration-200 ${
                  player.id === currentPlayerId
                    ? 'bg-indigo-900/60 border border-indigo-500/60 shadow-lg'
                    : 'bg-gray-800/60 hover:bg-gray-700/60'
                }`}
              >
                <div className="flex-none w-8 text-center">
                  {index === 0 ? (
                    <Crown size={18} className="text-yellow-400 mx-auto" />
                  ) : (
                    <span className={`text-sm font-bold ${
                      index === 1 ? 'text-gray-300' :
                      index === 2 ? 'text-amber-600' :
                      'text-gray-400'
                    }`}>
                      {index + 1}
                    </span>
                  )}
                </div>
                <div className="flex-1 text-sm truncate text-white ml-3 font-medium">
                  {player.name}
                </div>
                <div className="text-right text-sm font-bold text-indigo-300">
                  {player.score.toLocaleString()}
                </div>
              </div>
            ))}

            {/* Show current player if not in top 3 and board is collapsed */}
            {showCurrentPlayerSeparately && (
              <>
                <div className="border-t border-gray-700/50 my-2 pt-2">
                  <div
                    className="flex items-center px-3 py-2 rounded-lg bg-indigo-900/60 border border-indigo-500/60 shadow-lg"
                  >
                    <div className="flex-none w-8 text-center">
                      <span className="text-sm font-bold text-gray-400">
                        {currentPlayerPosition + 1}
                      </span>
                    </div>
                    <div className="flex-1 text-sm truncate text-white ml-3 font-medium">
                      {players[currentPlayerPosition].name}
                    </div>
                    <div className="text-right text-sm font-bold text-indigo-300">
                      {players[currentPlayerPosition].score.toLocaleString()}
                    </div>
                  </div>
                </div>
              </>
            )}
          </>
        )}
      </div>

      {/* Player count */}
      {!isExpanded && players.length > 3 && (
        <div className="border-t border-gray-700/50 px-4 py-2 text-center text-sm text-gray-400">
          <button
            onClick={() => setIsExpanded(true)}
            className="hover:text-white transition-colors font-medium"
          >
            Show all players ({players.length})
          </button>
        </div>
      )}
    </div>
  );
} 