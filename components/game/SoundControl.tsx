"use client";

import { useState, useEffect, useCallback } from "react";
import { Volume2, VolumeX, Music, Info } from "lucide-react";
import { soundManager } from "@/lib/audio/SoundManager";

const SoundControl = () => {
  const [isMuted, setIsMuted] = useState(false);
  const [musicVolume, setMusicVolume] = useState(0.3);
  const [sfxVolume, setSfxVolume] = useState(0.5);
  const [showControls, setShowControls] = useState(false);
  const [showInfoMessage, setShowInfoMessage] = useState(false);

  useEffect(() => {
    // Initialize state from sound manager
    setIsMuted(soundManager.isMutedStatus());
  }, []);

  const handleToggleMute = useCallback(() => {
    const newMuteState = soundManager.toggleMute();
    setIsMuted(newMuteState);
  }, []);

  const handleMusicVolumeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setMusicVolume(newVolume);
    soundManager.setMusicVolume(newVolume);
  }, []);

  const handleSfxVolumeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setSfxVolume(newVolume);
    soundManager.setSfxVolume(newVolume);
  }, []);

  const toggleControlsVisibility = useCallback(() => {
    setShowControls(!showControls);
  }, [showControls]);

  const handleTestSfx = useCallback(() => {
    soundManager.playRandomFoodSound();
  }, []);

  const toggleInfoMessage = useCallback(() => {
    setShowInfoMessage(!showInfoMessage);
  }, [showInfoMessage]);

  return (
    <div className="flex flex-col items-end space-y-3">
      {/* Info message for placeholder sounds */}
      {showInfoMessage && (
        <div className="p-4 bg-black/80 backdrop-blur-md rounded-lg w-72 text-sm text-white border border-yellow-500/30 shadow-xl">
          <p className="mb-3 text-yellow-300 font-semibold">Sound Files Notice</p>
          <p className="mb-3 text-gray-200">
            The sound files in this project are placeholders. To experience the full game with sound:
          </p>
          <ol className="list-decimal pl-4 mb-3 space-y-1 text-gray-300">
            <li>Download audio files for games (see README in public/sounds folder)</li>
            <li>Add them to the public/sounds directory with matching filenames</li>
            <li>Refresh the page</li>
          </ol>
          <button
            onClick={toggleInfoMessage}
            className="mt-2 px-3 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg text-center w-full transition-colors"
          >
            Close
          </button>
        </div>
      )}

      {/* Expanded volume controls */}
      {showControls && (
        <div className="p-4 bg-black/80 backdrop-blur-md rounded-lg flex flex-col gap-3 w-56 border border-indigo-500/30 shadow-xl">
          <div className="flex items-center gap-3">
            <Music size={16} className="text-indigo-400" />
            <span className="text-sm text-white font-medium">Music</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={musicVolume}
              onChange={handleMusicVolumeChange}
              className="flex-1 accent-indigo-500"
            />
          </div>

          <div className="flex items-center gap-3">
            <Volume2 size={16} className="text-cyan-400" />
            <span className="text-sm text-white font-medium">SFX</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={sfxVolume}
              onChange={handleSfxVolumeChange}
              className="flex-1 accent-cyan-500"
            />
          </div>

          <div className="flex items-center justify-between pt-2 border-t border-gray-700/50">
            <button
              className="text-sm text-gray-300 hover:text-white px-3 py-1 bg-gray-800/60 hover:bg-gray-700/60 rounded-lg transition-colors"
              onClick={handleTestSfx}
            >
              Test Sound
            </button>

            <button
              className="text-sm text-yellow-300 hover:text-yellow-200 px-3 py-1 rounded-lg flex items-center gap-2 transition-colors"
              onClick={toggleInfoMessage}
            >
              <Info size={14} />
              Info
            </button>
          </div>
        </div>
      )}

      {/* Main sound button */}
      <button
        onClick={toggleControlsVisibility}
        className="group relative bg-indigo-900/70 hover:bg-indigo-800/90 text-white p-3 rounded-lg transition-all duration-200 shadow-lg border border-indigo-500/30"
      >
        <span className="sr-only">Sound Controls</span>
        <div onClick={handleToggleMute} className="flex items-center justify-center w-8 h-8">
          {isMuted ? (
            <VolumeX size={20} className="text-red-400" />
          ) : (
            <Volume2 size={20} className="text-cyan-400" />
          )}
        </div>
      </button>
    </div>
  );
};

export default SoundControl; 