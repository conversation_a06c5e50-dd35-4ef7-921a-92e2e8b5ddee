export interface Effect {
  x: number;
  y: number;
  type: 'glow' | 'pulse' | 'ring' | 'flash';
  color: string;
  size: number;
  life: number;
  maxLife: number;
  alpha: number;
  data?: any; // Additional effect-specific data
}

export class EffectSystem {
  effects: Effect[] = [];
  maxEffects: number = 500; // Limit total effects for performance
  
  createGlowEffect(x: number, y: number, color: string, size: number = 20, duration: number = 1): void {
    // Limit effect count if we're approaching the max
    if (this.effects.length >= this.maxEffects) {
      return;
    }
    
    this.effects.push({
      x,
      y,
      type: 'glow',
      color,
      size,
      life: duration,
      maxLife: duration,
      alpha: 1
    });
  }
  
  createPulseEffect(x: number, y: number, color: string, size: number = 30, duration: number = 0.8): void {
    if (this.effects.length >= this.maxEffects) {
      return;
    }
    
    this.effects.push({
      x,
      y,
      type: 'pulse',
      color,
      size,
      life: duration,
      maxLife: duration,
      alpha: 1
    });
  }
  
  createRingEffect(x: number, y: number, color: string, size: number = 40, duration: number = 1.2): void {
    if (this.effects.length >= this.maxEffects) {
      return;
    }
    
    this.effects.push({
      x,
      y,
      type: 'ring',
      color,
      size,
      life: duration,
      maxLife: duration,
      alpha: 1
    });
  }
  
  createFlashEffect(x: number, y: number, color: string, size: number = 50, duration: number = 0.3): void {
    if (this.effects.length >= this.maxEffects) {
      return;
    }
    
    this.effects.push({
      x,
      y,
      type: 'flash',
      color,
      size,
      life: duration,
      maxLife: duration,
      alpha: 1
    });
  }
  
  createPowerUpEffect(x: number, y: number, type: string): void {
    // Create different effects based on power-up type
    switch (type) {
      case 'giant':
        this.createRingEffect(x, y, '#FFD700', 60, 1.5);
        this.createGlowEffect(x, y, '#FFD700', 40, 2);
        break;
      case 'shield':
        this.createRingEffect(x, y, '#00BFFF', 50, 1.2);
        this.createPulseEffect(x, y, '#87CEEB', 35, 1);
        break;
      case 'ghost':
        this.createGlowEffect(x, y, '#9370DB', 45, 1.8);
        this.createFlashEffect(x, y, '#DDA0DD', 30, 0.5);
        break;
      case 'magnet':
        this.createRingEffect(x, y, '#FF6347', 55, 1.3);
        this.createPulseEffect(x, y, '#FF4500', 40, 1.1);
        break;
      default:
        this.createGlowEffect(x, y, '#FFFFFF', 30, 1);
    }
  }
  
  update(deltaTime: number): void {
    const decay = deltaTime / 1000; // Convert to seconds
    
    // If we have too many effects, decay them faster
    const decayMultiplier = 
      this.effects.length > this.maxEffects * 0.8 ? 
        1.5 : // Faster decay when near capacity
        1.0;  // Normal decay otherwise
    
    // Use filter for effects
    this.effects = this.effects.filter(effect => {
      // Update life with decay rate
      effect.life -= decay * decayMultiplier;
      effect.alpha = effect.life / effect.maxLife;
      
      // Keep if still alive
      return effect.life > 0;
    });
  }
  
  draw(ctx: CanvasRenderingContext2D, cameraX: number, cameraY: number, zoom: number, width: number, height: number): void {
    // Only draw effects that are in view
    const screenLeft = cameraX - width / 2 / zoom;
    const screenRight = cameraX + width / 2 / zoom;
    const screenTop = cameraY - height / 2 / zoom;
    const screenBottom = cameraY + height / 2 / zoom;
    
    // Batch drawing by setting common properties once
    ctx.save();
    
    // Draw effects in a single loop for better performance
    for (const effect of this.effects) {
      // Skip effects outside the view for performance
      if (
        effect.x < screenLeft - effect.size ||
        effect.x > screenRight + effect.size ||
        effect.y < screenTop - effect.size ||
        effect.y > screenBottom + effect.size
      ) {
        continue;
      }
      
      // Convert world coordinates to screen coordinates
      const screenX = (effect.x - cameraX) * zoom + width / 2;
      const screenY = (effect.y - cameraY) * zoom + height / 2;
      const screenSize = effect.size * zoom;
      
      ctx.globalAlpha = effect.alpha;
      
      switch (effect.type) {
        case 'glow':
          this.drawGlow(ctx, screenX, screenY, screenSize, effect.color);
          break;
        case 'pulse':
          this.drawPulse(ctx, screenX, screenY, screenSize, effect.color, effect.life / effect.maxLife);
          break;
        case 'ring':
          this.drawRing(ctx, screenX, screenY, screenSize, effect.color, effect.life / effect.maxLife);
          break;
        case 'flash':
          this.drawFlash(ctx, screenX, screenY, screenSize, effect.color);
          break;
      }
    }
    
    // Restore the context once after all effects
    ctx.restore();
  }
  
  private drawGlow(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string): void {
    const gradient = ctx.createRadialGradient(x, y, 0, x, y, size);
    gradient.addColorStop(0, color);
    gradient.addColorStop(1, 'rgba(0,0,0,0)');
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.fill();
  }
  
  private drawPulse(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string, progress: number): void {
    const pulseSize = size * (1 + Math.sin(progress * Math.PI * 4) * 0.3);
    this.drawGlow(ctx, x, y, pulseSize, color);
  }
  
  private drawRing(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string, progress: number): void {
    const ringSize = size * (1 - progress);
    const thickness = 3;
    
    ctx.strokeStyle = color;
    ctx.lineWidth = thickness;
    ctx.beginPath();
    ctx.arc(x, y, ringSize, 0, Math.PI * 2);
    ctx.stroke();
  }
  
  private drawFlash(ctx: CanvasRenderingContext2D, x: number, y: number, size: number, color: string): void {
    ctx.fillStyle = color;
    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.fill();
  }
  
  clear(): void {
    this.effects = [];
  }
}
