#!/bin/bash

# Setup script for Snake.io game development environment
set -e

echo "Setting up Snake.io game development environment..."

# Update system packages
sudo apt-get update

# Install Node.js 18+ (LTS)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js installation
node --version
npm --version

# Navigate to project directory
cd /mnt/persist/workspace

# Install project dependencies
echo "Installing project dependencies..."
npm install

# Install additional development dependencies for testing and code quality
echo "Installing development dependencies..."
npm install --save-dev jest @types/jest jest-environment-jsdom @testing-library/react @testing-library/jest-dom @testing-library/user-event eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser prettier eslint-config-prettier eslint-plugin-prettier

# Create necessary directories if they don't exist
mkdir -p tests
mkdir -p tests/unit
mkdir -p tests/integration
mkdir -p tests/__mocks__

# Set up environment variables
echo "Setting up environment variables..."
if [ ! -f .env.local ]; then
    cp .env.local.smtp .env.local || echo "# Environment variables" > .env.local
fi

# Build the project to check for compilation errors
echo "Building the project..."
npm run build

echo "Development environment setup complete!"